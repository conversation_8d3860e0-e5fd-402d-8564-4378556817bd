

{{ 'quiz-popup.css' | asset_url | stylesheet_tag: media: 'screen' }}
<div class="quiz-popup">
  <div class="quiz-popup-close">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 640">
      <path fill="#ffffff" d="M183.1 137.4C170.6 124.9 150.3 124.9 137.8 137.4C125.3 149.9 125.3 170.2 137.8 182.7L275.2 320L137.9 457.4C125.4 469.9 125.4 490.2 137.9 502.7C150.4 515.2 170.7 515.2 183.2 502.7L320.5 365.3L457.9 502.6C470.4 515.1 490.7 515.1 503.2 502.6C515.7 490.1 515.7 469.8 503.2 457.3L365.8 320L503.1 182.6C515.6 170.1 515.6 149.8 503.1 137.3C490.6 124.8 470.3 124.8 457.8 137.3L320.5 274.7L183.1 137.4z"/>
    </svg>
  </div>
  <div class="quiz-popup-content">
    <div class="quiz-popup-pair">
      <div class="quiz-pair-heading">
        <h3>Are you buying this pair for you or for someone else?</h3>
      </div>
      <div class="quiz-pair-wrapper">
        <label class="quiz-pair quiz-pair-for-me">
          <input type="radio" name="quiz-option" value="For me">
          <div class="quiz-pair-box">
            <img src="https://dcpsvjmu5hpqr.cloudfront.net/images/2023-09/70b9c0acc458494815d67617.webp">
            <span class="quiz-pair-title">For me</span>
          </div>
        </label>

        <label class="quiz-pair quiz-pair-for-someone-else">
          <input type="radio" name="quiz-option" value="For someone else">
          <div class="quiz-pair-box">
            <img src="https://dcpsvjmu5hpqr.cloudfront.net/images/2023-09/624fc257288a6bf8e5477234.webp">
            <span class="quiz-pair-title">For someone else</span>
          </div>
        </label>
      </div>
      <div class="quiz-pair-submit">
        <button class="pair-submit-btn" type="submit" disabled>NEXT</button>
      </div>
    </div>
    <div class="quiz-popup-iframe">
      <iframe id="Quiz_quizell_Iframe" src=""></iframe>
    </div>
  </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
  $(document).ready(function () {
    const $submitButton = $('.pair-submit-btn');
    const $radioButtons = $('input[name="quiz-option"]');
    const $iframeContainer = $('.quiz-popup-iframe');
    const $pairContainer = $('.quiz-popup-pair');
    const $iframe = $('#Quiz_quizell_Iframe');

    var cartItemCount = {{ cart.item_count | default: 0 }};
    var baseQuizURL = "https://app.quizell.com/quiz/";

    var quizKeyEmpty = "C2lg1jpFCSjr0xpg84esMS2189100";
    var quizKeyForMe = "uxsjNkERnBGvlaKETq3CMS2188900";
    var quizKeyForSomeoneElse = "b02UmQtHdeLrLqtB5NiiMS2180800";

    {% if template.name == 'product' %}
      quizKeyEmpty = {% if product and product.metafields.custom.quiz_key %}"{{ product.metafields.custom.quiz_key }}"{% endif %};
      quizKeyForMe = {% if product and product.metafields.custom.quiz_for_me %}"{{ product.metafields.custom.quiz_for_me }}"{% endif %};
      quizKeyForSomeoneElse = {% if product and product.metafields.custom.quiz_for_someone_else %}"{{ product.metafields.custom.quiz_for_someone_else }}"{% endif %};
    {% endif %}

    $radioButtons.on('change', function () {
      const isAnyChecked = $radioButtons.is(':checked');
      $submitButton.prop('disabled', !isAnyChecked);
    });

    $(document).on('click', '.custom-button-container .custom-button-link, .steps-cta .steps-cta-button', function(e) {
      e.preventDefault();
      if (cartItemCount === 0) {
        $iframe.attr('src', baseQuizURL + quizKeyEmpty);
        $pairContainer.hide();
        $iframeContainer.show();
      } else {
        $pairContainer.show();
        $iframeContainer.hide();
      }
      $('.quiz-popup').show();
    });

    $submitButton.off('click').on('click', function () {
      var selectedOption = $('input[name="quiz-option"]:checked').val();
      var quizKey;

      if (selectedOption === "For me") {
        quizKey = quizKeyForMe;
      } else if (selectedOption === "For someone else") {
        quizKey = quizKeyForSomeoneElse;
      } else {
        alert('Please select an option first');
        return;
      }

      $iframe.attr('src', baseQuizURL + quizKey);
      $iframeContainer.show();
      $pairContainer.hide();
    });

    $(document).on('click', '.quiz-popup-close', function() {
      $('.quiz-popup').hide();
      $iframe.attr('src', '');
    });
  });
</script>

