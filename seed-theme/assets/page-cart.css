/* Custom Cart Styles */

/* Cart Product Title Styling */
.l4ca li header h2,
.l4ca li header h2 a,
.form-cart .l4ca li header h2,
.form-cart .l4ca li header h2 a {
	font-weight: 400 !important;
	font-family: Lato, sans-serif !important;
	line-height: 18.4px !important;
	letter-spacing: normal !important;
	font-size: 16px !important;
	color: #009ee0 !important;
	text-decoration: none !important;
}

.l4ca li header h2 a:hover,
.form-cart .l4ca li header h2 a:hover {
	color: #007bb8 !important;
	text-decoration: none !important;
}

/* Checkout Button Styling */
.form-cart button[name="checkout"],
.form-cart .overlay-buy_button button,
button.overlay-buy_button,
.overlay-buy_button,
button.overlay-primary,
button.overlay-secondary,
button.overlay-tertiary,
button.overlay-dynamic_buy_button,
.overlay-primary,
.overlay-secondary,
.overlay-tertiary,
.overlay-dynamic_buy_button {
	background-color: #00A2F5 !important;
	border-color: #00A2F5 !important;
}

.form-cart button[name="checkout"]:before,
.form-cart .overlay-buy_button button:before,
button.overlay-buy_button:before,
.overlay-buy_button:before,
button.overlay-primary:before,
button.overlay-secondary:before,
button.overlay-tertiary:before,
button.overlay-dynamic_buy_button:before,
.overlay-primary:before,
.overlay-secondary:before,
.overlay-tertiary:before,
.overlay-dynamic_buy_button:before {
	background-color: #00A2F5 !important;
	border-color: #00A2F5 !important;
}

.form-cart button[name="checkout"]:hover,
.form-cart .overlay-buy_button button:hover,
button.overlay-buy_button:hover,
.overlay-buy_button:hover,
button.overlay-primary:hover,
button.overlay-secondary:hover,
button.overlay-tertiary:hover,
button.overlay-dynamic_buy_button:hover,
.overlay-primary:hover,
.overlay-secondary:hover,
.overlay-tertiary:hover,
.overlay-dynamic_buy_button:hover {
	background-color: #0091d9 !important;
	border-color: #0091d9 !important;
}

.form-cart button[name="checkout"]:hover:before,
.form-cart .overlay-buy_button button:hover:before,
button.overlay-buy_button:hover:before,
.overlay-buy_button:hover:before,
button.overlay-primary:hover:before,
button.overlay-secondary:hover:before,
button.overlay-tertiary:hover:before,
button.overlay-dynamic_buy_button:hover:before,
.overlay-primary:hover:before,
.overlay-secondary:hover:before,
.overlay-tertiary:hover:before,
.overlay-dynamic_buy_button:hover:before {
	background-color: #0091d9 !important;
	border-color: #0091d9 !important;
}

/* Cart Text Styling */
.l4ca li section ul li,
.l4ca li section p:not(.price),
.form-cart .l4ca li section ul li,
.form-cart .l4ca li section p:not(.price),
.cart-item-options,
.cart-item-options span,
.l4ca li section span,
.form-cart .l4ca li section span,
.l4ca li section header p:not(.price),
.form-cart .l4ca li section header p:not(.price) {
	font-size: 14px !important;
	color: #00AEF8 !important;
}

/* Variant options and product properties styling */
.l4ca li section ul li,
.form-cart .l4ca li section ul li {
	font-size: 14px !important;
	color: #00AEF8 !important;
	font-family: Lato, sans-serif !important;
}

/* Product delivery time and other info */
.l4ca li section .size-12,
.form-cart .l4ca li section .size-12,
.l4ca li section .overlay-gray,
.form-cart .l4ca li section .overlay-gray {
	font-size: 14px !important;
	color: #00AEF8 !important;
}

/* Additional cart styling for better coverage */
.l4ca li section,
.form-cart .l4ca li section {
	font-family: Lato, sans-serif !important;
}

/* Ensure cart item options are styled correctly */
.l4ca li section ul,
.form-cart .l4ca li section ul {
	margin: 0 !important;
	padding: 0 !important;
	list-style: none !important;
}

/* Side cart styling (drawer) */
.m6pc .l4ca li header h3,
.m6pc .l4ca li header h3 a {
	font-weight: 400 !important;
	font-family: Lato, sans-serif !important;
	line-height: 18.4px !important;
	letter-spacing: normal !important;
	font-size: 16px !important;
	color: #009ee0 !important;
	text-decoration: none !important;
}

.m6pc .l4ca li header h3 a:hover {
	color: #007bb8 !important;
	text-decoration: none !important;
}

.m6ca:before { content: ""; display: block; overflow: hidden; position: absolute; left: 0; top: 0; right: 0; bottom: 0; z-index: -1; margin: 0; text-align: left; text-indent: -3000em; direction: ltr; }
.m6ca { position: relative; z-index: 2; margin-bottom: 20px; padding: 16px 16px 1px 350px; }
	.m6ca:before { background: var(--sand); }
	.m6ca > * { width: 100%; margin-bottom: 0; padding-top: 6px; }
	.m6ca > header { width: 350px; margin-left: -350px; padding-left: 25px; padding-right: 25px; }
	.m6ca > .l4cl { margin-top: 0; margin-bottom: 0; padding-top: 0; }
	.m6ca .l4cl li { margin-bottom: 16px; padding: 20px 20px 16px; }
	.m6ca .l4cl li:before { background: var(--white); }
.m6ca {
	display: flex;
	justify-content: space-between;
}
@media only screen and (max-width: 1356px) {
.m6ca { padding-left: 335px; }
	.m6ca > header { width: 335px; margin-left: -335px; }
}
@media only screen and (max-width: 1200px) {
.m6ca { padding-left: 320px; }
	.m6ca > header { width: 320px; margin-left: -320px; }
	#root .m6ca .l4cl.list .price span { display: block; margin-left: 0; margin-right: 0; line-height: var(--main_lh_h); }
}
@media only screen and (max-width: 1100px) { 
.m6ca, #root .m6ca { display: block; padding-left: 16px; padding-right: 16px; }
	.m6ca > header, #root .m6ca > header { width: auto; margin-left: 0; margin-right: 0; padding-left: 9px; padding-right: 9px; }
	#root .m6ca .l4cl.list .price span { display: inline; margin-left: 0; margin-right: 2px; }
}
@media only screen and (max-width: 760px) {
.m6ca, #root .m6ca { margin-left: var(--rpn); margin-right: var(--rpn); padding-left: var(--rpp); padding-right: var(--rpp); padding-top: 16px; border-top-width: 0; }
	#root .m6ca > header { padding: 0; }
	.m6ca > header h1, .m6ca > header h2, .m6ca > header h3, .m6ca > header h4, .m6ca > header h5, .m6ca > header h6 { margin-bottom: 8px; font-size: var(--size_16_f); }
	.m6ca > .l4cl { margin-bottom: 20px; }
		.m6ca > header + .l4cl { margin-top: -4px; }
}