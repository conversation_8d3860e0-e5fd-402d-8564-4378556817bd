.m6ca:before { content: ""; display: block; overflow: hidden; position: absolute; left: 0; top: 0; right: 0; bottom: 0; z-index: -1; margin: 0; text-align: left; text-indent: -3000em; direction: ltr; }
.m6ca { position: relative; z-index: 2; margin-bottom: 20px; padding: 16px 16px 1px 350px; }
	.m6ca:before { background: var(--sand); }
	.m6ca > * { width: 100%; margin-bottom: 0; padding-top: 6px; }
	.m6ca > header { width: 350px; margin-left: -350px; padding-left: 25px; padding-right: 25px; }
	.m6ca > .l4cl { margin-top: 0; margin-bottom: 0; padding-top: 0; }
	.m6ca .l4cl li { margin-bottom: 16px; padding: 20px 20px 16px; }
	.m6ca .l4cl li:before { background: var(--white); }
.m6ca { 
	display: flex;
	justify-content: space-between; 
}
@media only screen and (max-width: 1356px) {
.m6ca { padding-left: 335px; }
	.m6ca > header { width: 335px; margin-left: -335px; }
}
@media only screen and (max-width: 1200px) {
.m6ca { padding-left: 320px; }
	.m6ca > header { width: 320px; margin-left: -320px; }
	#root .m6ca .l4cl.list .price span { display: block; margin-left: 0; margin-right: 0; line-height: var(--main_lh_h); }
}
@media only screen and (max-width: 1100px) { 
.m6ca, #root .m6ca { display: block; padding-left: 16px; padding-right: 16px; }
	.m6ca > header, #root .m6ca > header { width: auto; margin-left: 0; margin-right: 0; padding-left: 9px; padding-right: 9px; }
	#root .m6ca .l4cl.list .price span { display: inline; margin-left: 0; margin-right: 2px; }
}
@media only screen and (max-width: 760px) {
.m6ca, #root .m6ca { margin-left: var(--rpn); margin-right: var(--rpn); padding-left: var(--rpp); padding-right: var(--rpp); padding-top: 16px; border-top-width: 0; }
	#root .m6ca > header { padding: 0; }
	.m6ca > header h1, .m6ca > header h2, .m6ca > header h3, .m6ca > header h4, .m6ca > header h5, .m6ca > header h6 { margin-bottom: 8px; font-size: var(--size_16_f); }
	.m6ca > .l4cl { margin-bottom: 20px; }
		.m6ca > header + .l4cl { margin-top: -4px; }
}

/* ========================================
   CUSTOM CART PAGE STYLES - REFACTORED
   ======================================== */

/* Cart Product Title Styling - Specific to cart items only */
.form-cart .l4ca li header h2,
.form-cart .l4ca li header h2 a {
	font-weight: 400 !important;
	font-family: Lato, sans-serif !important;
	line-height: 18.4px !important;
	letter-spacing: normal !important;
	font-size: 16px !important;
	color: #009ee0 !important;
	text-decoration: none !important;
	text-align: start !important;
	margin: 0 !important;
}

.form-cart .l4ca li header h2 a:hover {
	color: #007bb8 !important;
	text-decoration: none !important;
}

/* Cart Text Styling - Only for specific delivery/info elements */
.form-cart .l4ca li section .size-12,
.form-cart .l4ca li section .overlay-gray {
	font-size: 16px !important;
	font-family: Lato, sans-serif !important;
	color: #00AEF8 !important;
	font-weight: 900 !important;
}

/* Checkout Button Styling - Scoped to cart page only */
.form-cart button[name="checkout"] {
	background-color: #00A2F5 !important;
	border-color: #00A2F5 !important;
}

.form-cart button[name="checkout"]:before {
	background-color: #00A2F5 !important;
	border-color: #00A2F5 !important;
}

.form-cart button[name="checkout"]:hover {
	background-color: #0091d9 !important;
	border-color: #0091d9 !important;
}

.form-cart button[name="checkout"]:hover:before {
	background-color: #0091d9 !important;
	border-color: #0091d9 !important;
}

/* Dynamic Buy Button in Cart */
.form-cart .overlay-buy_button,
.form-cart .overlay-buy_button button {
	background-color: #00A2F5 !important;
	border-color: #00A2F5 !important;
}

.form-cart .overlay-buy_button:before,
.form-cart .overlay-buy_button button:before {
	background-color: #00A2F5 !important;
	border-color: #00A2F5 !important;
}

.form-cart .overlay-buy_button:hover,
.form-cart .overlay-buy_button button:hover {
	background-color: #0091d9 !important;
	border-color: #0091d9 !important;
}

.form-cart .overlay-buy_button:hover:before,
.form-cart .overlay-buy_button button:hover:before {
	background-color: #0091d9 !important;
	border-color: #0091d9 !important;
}

/* Cart Page Title Styling - Using specific custom class */
.custom-cart-page .custom-cart-title {
	color: #5a667e !important;
	font-family: Lato, sans-serif !important;
	font-size: 36px !important;
	font-style: normal !important;
	font-weight: 700 !important;
	letter-spacing: 0.25px !important;
	line-height: normal !important;
	margin: 0 auto 16px !important;
	text-align: center !important;
}

/* Cart Subtitle with item count and prices - Specific class */
.cart-subtitle {
	color: #8c94a4 !important;
	font-family: Lato, sans-serif !important;
	font-size: 14px !important;
	font-style: normal !important;
	font-weight: 400 !important;
	letter-spacing: 0.098px !important;
	line-height: normal !important;
	margin-bottom: 30px !important;
	text-align: center !important;
}

.cart-subtitle .old-price {
	color: #8c94a4 !important;
	text-decoration: line-through !important;
	margin: 0 5px !important;
}

.cart-subtitle .total {
	color: #4f5767 !important;
	font-family: Lato, sans-serif !important;
	font-size: 14px !important;
	font-style: normal !important;
	font-weight: 400 !important;
	letter-spacing: 0.098px !important;
	line-height: normal !important;
	margin-left: 5px !important;
}

/* Cart Summary Styling - Scoped to cart page only */
.form-cart aside .l4tt li {
	color: #8c94a4 !important;
	font-family: Lato, sans-serif !important;
	font-size: 14px !important;
	font-style: normal !important;
	font-weight: 400 !important;
	letter-spacing: 0.098px !important;
	line-height: normal !important;
	text-align: center !important;
}

.form-cart aside .l4tt li.strong {
	color: #4f5767 !important;
	font-family: Lato, sans-serif !important;
	font-size: 14px !important;
	font-style: normal !important;
	font-weight: 400 !important;
	letter-spacing: 0.098px !important;
	line-height: normal !important;
	text-align: center !important;
}

.form-cart aside .l4tt {
	text-align: center !important;
	margin-bottom: 60px !important;
}

/* ========================================
   MOBILE RESPONSIVE ADJUSTMENTS
   ======================================== */
@media only screen and (max-width: 768px) {
	/* Cart Title Mobile */
	.custom-cart-page .custom-cart-title {
		font-size: 28px !important;
		margin: 0 auto 12px !important;
	}

	/* Cart Subtitle Mobile */
	.cart-subtitle {
		margin-bottom: 40px !important;
		font-size: 12px !important;
	}

	/* Cart Summary Mobile */
	.form-cart aside .l4tt {
		margin-bottom: 40px !important;
	}
}