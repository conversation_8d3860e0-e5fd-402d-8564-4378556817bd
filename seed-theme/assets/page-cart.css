.m6ca:before { content: ""; display: block; overflow: hidden; position: absolute; left: 0; top: 0; right: 0; bottom: 0; z-index: -1; margin: 0; text-align: left; text-indent: -3000em; direction: ltr; }
.m6ca { position: relative; z-index: 2; margin-bottom: 20px; padding: 16px 16px 1px 350px; }
	.m6ca:before { background: var(--sand); }
	.m6ca > * { width: 100%; margin-bottom: 0; padding-top: 6px; }
	.m6ca > header { width: 350px; margin-left: -350px; padding-left: 25px; padding-right: 25px; }
	.m6ca > .l4cl { margin-top: 0; margin-bottom: 0; padding-top: 0; }
	.m6ca .l4cl li { margin-bottom: 16px; padding: 20px 20px 16px; }
	.m6ca .l4cl li:before { background: var(--white); }
.m6ca { 
	display: flex;
	justify-content: space-between; 
}
@media only screen and (max-width: 1356px) {
.m6ca { padding-left: 335px; }
	.m6ca > header { width: 335px; margin-left: -335px; }
}
@media only screen and (max-width: 1200px) {
.m6ca { padding-left: 320px; }
	.m6ca > header { width: 320px; margin-left: -320px; }
	#root .m6ca .l4cl.list .price span { display: block; margin-left: 0; margin-right: 0; line-height: var(--main_lh_h); }
}
@media only screen and (max-width: 1100px) { 
.m6ca, #root .m6ca { display: block; padding-left: 16px; padding-right: 16px; }
	.m6ca > header, #root .m6ca > header { width: auto; margin-left: 0; margin-right: 0; padding-left: 9px; padding-right: 9px; }
	#root .m6ca .l4cl.list .price span { display: inline; margin-left: 0; margin-right: 2px; }
}
@media only screen and (max-width: 760px) {
.m6ca, #root .m6ca { margin-left: var(--rpn); margin-right: var(--rpn); padding-left: var(--rpp); padding-right: var(--rpp); padding-top: 16px; border-top-width: 0; }
	#root .m6ca > header { padding: 0; }
	.m6ca > header h1, .m6ca > header h2, .m6ca > header h3, .m6ca > header h4, .m6ca > header h5, .m6ca > header h6 { margin-bottom: 8px; font-size: var(--size_16_f); }
	.m6ca > .l4cl { margin-bottom: 20px; }
		.m6ca > header + .l4cl { margin-top: -4px; }
}

/* Custom Cart Styles */

/* Cart Product Title Styling */
.l4ca li header h2,
.l4ca li header h2 a,
.form-cart .l4ca li header h2,
.form-cart .l4ca li header h2 a {
	font-weight: 400 !important;
	font-family: Lato, sans-serif !important;
	line-height: 18.4px !important;
	letter-spacing: normal !important;
	font-size: 16px !important;
	color: #009ee0 !important;
	text-decoration: none !important;
}

.l4ca li header h2 a:hover,
.form-cart .l4ca li header h2 a:hover {
	color: #007bb8 !important;
	text-decoration: none !important;
}

/* Checkout Button Styling */
.form-cart button[name="checkout"],
.form-cart .overlay-buy_button button,
button.overlay-buy_button,
.overlay-buy_button,
button.overlay-primary,
button.overlay-secondary,
button.overlay-tertiary,
button.overlay-dynamic_buy_button,
.overlay-primary,
.overlay-secondary,
.overlay-tertiary,
.overlay-dynamic_buy_button {
	background-color: #00A2F5 !important;
	border-color: #00A2F5 !important;
}

.form-cart button[name="checkout"]:before,
.form-cart .overlay-buy_button button:before,
button.overlay-buy_button:before,
.overlay-buy_button:before,
button.overlay-primary:before,
button.overlay-secondary:before,
button.overlay-tertiary:before,
button.overlay-dynamic_buy_button:before,
.overlay-primary:before,
.overlay-secondary:before,
.overlay-tertiary:before,
.overlay-dynamic_buy_button:before {
	background-color: #00A2F5 !important;
	border-color: #00A2F5 !important;
}

.form-cart button[name="checkout"]:hover,
.form-cart .overlay-buy_button button:hover,
button.overlay-buy_button:hover,
.overlay-buy_button:hover,
button.overlay-primary:hover,
button.overlay-secondary:hover,
button.overlay-tertiary:hover,
button.overlay-dynamic_buy_button:hover,
.overlay-primary:hover,
.overlay-secondary:hover,
.overlay-tertiary:hover,
.overlay-dynamic_buy_button:hover {
	background-color: #0091d9 !important;
	border-color: #0091d9 !important;
}

.form-cart button[name="checkout"]:hover:before,
.form-cart .overlay-buy_button button:hover:before,
button.overlay-buy_button:hover:before,
.overlay-buy_button:hover:before,
button.overlay-primary:hover:before,
button.overlay-secondary:hover:before,
button.overlay-tertiary:hover:before,
button.overlay-dynamic_buy_button:hover:before,
.overlay-primary:hover:before,
.overlay-secondary:hover:before,
.overlay-tertiary:hover:before,
.overlay-dynamic_buy_button:hover:before {
	background-color: #0091d9 !important;
	border-color: #0091d9 !important;
}

/* Cart text styling - only for specific elements */
.l4ca li section .size-12,
.form-cart .l4ca li section .size-12,
.l4ca li section .overlay-gray,
.form-cart .l4ca li section .overlay-gray {
	font-size: 16px !important;
	font-family: Lato, sans-serif !important;
	color: #00AEF8 !important;
	font-weight: 900 !important;
}

/* Cart Page Title Styling */
.form-cart h1,
.form-cart h2,
.form-cart h3,
.form-cart h4,
.form-cart h5,
.form-cart h6,
main h1,
main h2,
main h3,
main h4,
main h5,
main h6 {
	color: #5a667e !important;
	font-family: Lato, sans-serif !important;
	font-size: 36px !important;
	font-style: normal !important;
	font-weight: 700 !important;
	letter-spacing: 0.25px !important;
	line-height: normal !important;
	margin: 0 auto 16px !important;
	text-align: center !important;
}

/* Cart Summary Styling */
.l4tt li {
	color: #8c94a4 !important;
	font-family: Lato, sans-serif !important;
	font-size: 14px !important;
	font-style: normal !important;
	font-weight: 400 !important;
	letter-spacing: 0.098px !important;
	line-height: normal !important;
	text-align: center !important;
}

/* Cart Summary Total Price */
.l4tt li.strong {
	color: #4f5767 !important;
	font-family: Lato, sans-serif !important;
	font-size: 14px !important;
	font-style: normal !important;
	font-weight: 400 !important;
	letter-spacing: 0.098px !important;
	line-height: normal !important;
	text-align: center !important;
}

/* Cart Summary Container */
.l4tt {
	text-align: center !important;
	margin-bottom: 60px !important;
}

/* Mobile responsive adjustments */
@media only screen and (max-width: 768px) {
	.form-cart h1,
	.form-cart h2,
	.form-cart h3,
	.form-cart h4,
	.form-cart h5,
	.form-cart h6,
	main h1,
	main h2,
	main h3,
	main h4,
	main h5,
	main h6 {
		font-size: 28px !important;
		margin: 0 auto 12px !important;
	}

	.l4tt {
		margin-bottom: 40px !important;
	}
}