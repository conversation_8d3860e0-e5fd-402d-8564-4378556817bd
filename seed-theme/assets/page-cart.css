.m6ca:before { content: ""; display: block; overflow: hidden; position: absolute; left: 0; top: 0; right: 0; bottom: 0; z-index: -1; margin: 0; text-align: left; text-indent: -3000em; direction: ltr; }
.m6ca { position: relative; z-index: 2; margin-bottom: 20px; padding: 16px 16px 1px 350px; }
	.m6ca:before { background: var(--sand); }
	.m6ca > * { width: 100%; margin-bottom: 0; padding-top: 6px; }
	.m6ca > header { width: 350px; margin-left: -350px; padding-left: 25px; padding-right: 25px; }
	.m6ca > .l4cl { margin-top: 0; margin-bottom: 0; padding-top: 0; }
	.m6ca .l4cl li { margin-bottom: 16px; padding: 20px 20px 16px; }
	.m6ca .l4cl li:before { background: var(--white); }
.m6ca { 
	display: flex;
	justify-content: space-between; 
}
@media only screen and (max-width: 1356px) {
.m6ca { padding-left: 335px; }
	.m6ca > header { width: 335px; margin-left: -335px; }
}
@media only screen and (max-width: 1200px) {
.m6ca { padding-left: 320px; }
	.m6ca > header { width: 320px; margin-left: -320px; }
	#root .m6ca .l4cl.list .price span { display: block; margin-left: 0; margin-right: 0; line-height: var(--main_lh_h); }
}
@media only screen and (max-width: 1100px) { 
.m6ca, #root .m6ca { display: block; padding-left: 16px; padding-right: 16px; }
	.m6ca > header, #root .m6ca > header { width: auto; margin-left: 0; margin-right: 0; padding-left: 9px; padding-right: 9px; }
	#root .m6ca .l4cl.list .price span { display: inline; margin-left: 0; margin-right: 2px; }
}
@media only screen and (max-width: 760px) {
.m6ca, #root .m6ca { margin-left: var(--rpn); margin-right: var(--rpn); padding-left: var(--rpp); padding-right: var(--rpp); padding-top: 16px; border-top-width: 0; }
	#root .m6ca > header { padding: 0; }
	.m6ca > header h1, .m6ca > header h2, .m6ca > header h3, .m6ca > header h4, .m6ca > header h5, .m6ca > header h6 { margin-bottom: 8px; font-size: var(--size_16_f); }
	.m6ca > .l4cl { margin-bottom: 20px; }
		.m6ca > header + .l4cl { margin-top: -4px; }
}

/* ========================================
   CUSTOM CART PAGE STYLES - REFACTORED
   ======================================== */

/* New Cart Product Card Styling */
.form-cart .l4ca li.custom-cart-item {
	background: #fff !important;
	border: 1px solid #ededed !important;
	border-radius: 15px !important;
	box-shadow: 0 4px 22px 0 rgba(0, 0, 0, .15) !important;
	display: flex !important;
	flex-wrap: wrap !important;
	padding: 8px 14px 8px 8px !important;
	margin-bottom: 16px !important;
	position: relative !important;
}

.form-cart .l4ca li.custom-cart-item:before {
	display: none !important;
}

/* Cart Product Image Styling */
.form-cart .l4ca li.custom-cart-item figure {
	width: 232px !important;
	height: 184px !important;
	margin: 0 !important;
	flex-shrink: 0 !important;
	border-radius: 10px !important;
	overflow: hidden !important;
}

.form-cart .l4ca li.custom-cart-item figure picture {
	width: 100% !important;
	height: 100% !important;
	display: block !important;
}

.form-cart .l4ca li.custom-cart-item figure img {
	width: 100% !important;
	height: 100% !important;
	object-fit: cover !important;
	border-radius: 10px !important;
}

/* Cart Product Content Area */
.form-cart .l4ca li.custom-cart-item section {
	flex: 1 !important;
	padding-left: 16px !important;
	display: flex !important;
	flex-direction: column !important;
	justify-content: flex-start !important;
}

/* Cart Product Header - Move to top */
.form-cart .l4ca li.custom-cart-item header {
	order: -3 !important;
	margin-bottom: 8px !important;
}

/* Cart Product Title Styling */
.form-cart .l4ca li.custom-cart-item header h2,
.form-cart .l4ca li.custom-cart-item header h2 a {
	font-weight: 400 !important;
	font-family: Lato, sans-serif !important;
	line-height: 18.4px !important;
	letter-spacing: normal !important;
	font-size: 16px !important;
	color: #009ee0 !important;
	text-decoration: none !important;
	text-align: start !important;
	margin: 0 !important;
}

.form-cart .l4ca li.custom-cart-item header h2 a:hover {
	color: #007bb8 !important;
	text-decoration: none !important;
}

/* Cart Product Price Styling - Move under title */
.form-cart .l4ca li.custom-cart-item .price {
	order: -2 !important;
	margin: 4px 0 12px 0 !important;
	font-size: 16px !important;
	font-weight: 600 !important;
	color: #333 !important;
}

.form-cart .l4ca li.custom-cart-item .price .old-price {
	color: #999 !important;
	text-decoration: line-through !important;
	margin-right: 8px !important;
	font-weight: 400 !important;
}

/* Move mobile price to correct position */
.form-cart .l4ca li.custom-cart-item header .price {
	order: -2 !important;
	margin: 4px 0 12px 0 !important;
}

/* Cart Product Options/Variants */
.form-cart .l4ca li.custom-cart-item ul {
	margin: 8px 0 !important;
	padding: 0 !important;
	list-style: none !important;
	order: -1 !important;
}

.form-cart .l4ca li.custom-cart-item ul li {
	font-size: 14px !important;
	color: #666 !important;
	margin-bottom: 4px !important;
	font-family: Lato, sans-serif !important;
}

/* Cart Product Footer (Quantity & Remove) - Move to bottom */
.form-cart .l4ca li.custom-cart-item footer {
	display: flex !important;
	align-items: center !important;
	justify-content: space-between !important;
	margin-top: auto !important;
	padding-top: 12px !important;
	order: 10 !important;
}

.form-cart .l4ca li.custom-cart-item footer .input-amount {
	margin: 0 !important;
}

.form-cart .l4ca li.custom-cart-item footer .input-amount input {
	width: 60px !important;
	padding: 6px 8px !important;
	border: 1px solid #ddd !important;
	border-radius: 6px !important;
	text-align: center !important;
	font-size: 14px !important;
}

.form-cart .l4ca li.custom-cart-item footer .remove-from-cart-link {
	color: #999 !important;
	font-size: 18px !important;
	text-decoration: none !important;
	padding: 4px !important;
}

.form-cart .l4ca li.custom-cart-item footer .remove-from-cart-link:hover {
	color: #e74c3c !important;
}

/* Custom Cart Item - Discount/Coupon Text Styling */
.form-cart .l4ca li.custom-cart-item section .overlay-gray:has(.icon-label) {
	font-size: 12px !important;
	color: #959595 !important;
	font-family: Lato, sans-serif !important;
	margin: 4px 0 !important;
	display: flex !important;
	align-items: center !important;
	gap: 4px !important;
}

.form-cart .l4ca li.custom-cart-item section .overlay-gray:has(.icon-label) .icon-label {
	font-size: 12px !important;
}

/* Custom Cart Item - Sale Badge */
.form-cart .l4ca li.custom-cart-item .price .sale-badge {
	background: #e74c3c !important;
	color: white !important;
	padding: 2px 6px !important;
	border-radius: 4px !important;
	font-size: 12px !important;
	font-weight: 600 !important;
	margin-left: 8px !important;
	text-transform: uppercase !important;
}

/* Cart Text Styling - Only for specific delivery/info elements, excluding discount/coupon text */
.form-cart .l4ca li section .size-12:not(:has(.icon-label)),
.form-cart .l4ca li section .overlay-gray:not(:has(.icon-label)) {
	font-size: 16px !important;
	font-family: Lato, sans-serif !important;
	color: #00AEF8 !important;
	font-weight: 900 !important;
}

/* Ensure discount/coupon text keeps original styling */
.form-cart .l4ca li section .overlay-gray:has(.icon-label) {
	font-size: inherit !important;
	font-family: inherit !important;
	color: #959595 !important;
	font-weight: inherit !important;
}

/* Checkout Button Styling - Scoped to cart page only */
.form-cart button[name="checkout"] {
	background-color: #00A2F5 !important;
	border-color: #00A2F5 !important;
}

.form-cart button[name="checkout"]:before {
	background-color: #00A2F5 !important;
	border-color: #00A2F5 !important;
}

.form-cart button[name="checkout"]:hover {
	background-color: #0091d9 !important;
	border-color: #0091d9 !important;
}

.form-cart button[name="checkout"]:hover:before {
	background-color: #0091d9 !important;
	border-color: #0091d9 !important;
}

/* Dynamic Buy Button in Cart */
.form-cart .overlay-buy_button,
.form-cart .overlay-buy_button button {
	background-color: #00A2F5 !important;
	border-color: #00A2F5 !important;
}

.form-cart .overlay-buy_button:before,
.form-cart .overlay-buy_button button:before {
	background-color: #00A2F5 !important;
	border-color: #00A2F5 !important;
}

.form-cart .overlay-buy_button:hover,
.form-cart .overlay-buy_button button:hover {
	background-color: #0091d9 !important;
	border-color: #0091d9 !important;
}

.form-cart .overlay-buy_button:hover:before,
.form-cart .overlay-buy_button button:hover:before {
	background-color: #0091d9 !important;
	border-color: #0091d9 !important;
}

/* Cart Page Title Styling - Using specific custom class */
.custom-cart-page .custom-cart-title {
	color: #5a667e !important;
	font-family: Lato, sans-serif !important;
	font-size: 36px !important;
	font-style: normal !important;
	font-weight: 700 !important;
	letter-spacing: 0.25px !important;
	line-height: normal !important;
	margin: 0 auto 16px !important;
	text-align: center !important;
}

/* Cart Subtitle with item count and prices - Specific class */
.cart-subtitle {
	color: #8c94a4 !important;
	font-family: Lato, sans-serif !important;
	font-size: 14px !important;
	font-style: normal !important;
	font-weight: 400 !important;
	letter-spacing: 0.098px !important;
	line-height: normal !important;
	margin-bottom: 30px !important;
	text-align: center !important;
}

.cart-subtitle .old-price {
	color: #8c94a4 !important;
	text-decoration: line-through !important;
	margin: 0 5px !important;
}

.cart-subtitle .total {
	color: #4f5767 !important;
	font-family: Lato, sans-serif !important;
	font-size: 14px !important;
	font-style: normal !important;
	font-weight: 400 !important;
	letter-spacing: 0.098px !important;
	line-height: normal !important;
	margin-left: 5px !important;
}

/* Cart Summary Styling - Scoped to cart page only */
.form-cart aside .l4tt li {
	color: #8c94a4 !important;
	font-family: Lato, sans-serif !important;
	font-size: 14px !important;
	font-style: normal !important;
	font-weight: 400 !important;
	letter-spacing: 0.098px !important;
	line-height: normal !important;
	text-align: center !important;
}

.form-cart aside .l4tt li.strong {
	color: #4f5767 !important;
	font-family: Lato, sans-serif !important;
	font-size: 14px !important;
	font-style: normal !important;
	font-weight: 400 !important;
	letter-spacing: 0.098px !important;
	line-height: normal !important;
	text-align: center !important;
}

.form-cart aside .l4tt {
	text-align: center !important;
	margin-bottom: 60px !important;
}

/* ========================================
   RESPONSIVE ADJUSTMENTS
   ======================================== */

/* Tablet Styles */
@media only screen and (max-width: 1024px) and (min-width: 769px) {
	.form-cart .l4ca li.custom-cart-item figure {
		width: 180px !important;
		height: 144px !important;
	}

	.form-cart .l4ca li.custom-cart-item header h2,
	.form-cart .l4ca li.custom-cart-item header h2 a {
		font-size: 15px !important;
		line-height: 17px !important;
	}
}

/* Mobile Styles */
@media only screen and (max-width: 768px) {
	/* Cart Title Mobile */
	.custom-cart-page .custom-cart-title {
		font-size: 28px !important;
		margin: 0 auto 12px !important;
	}

	/* Cart Subtitle Mobile */
	.cart-subtitle {
		margin-bottom: 40px !important;
		font-size: 12px !important;
	}

	/* Cart Summary Mobile */
	.form-cart aside .l4tt {
		margin-bottom: 40px !important;
	}

	/* Custom Cart Item Mobile Styles - Keep horizontal layout but smaller */
	.form-cart .l4ca li.custom-cart-item {
		flex-direction: row !important;
		padding: 8px !important;
		margin-bottom: 12px !important;
	}

	.form-cart .l4ca li.custom-cart-item figure {
		width: 80px !important;
		height: 80px !important;
		margin: 0 !important;
		flex-shrink: 0 !important;
	}

	.form-cart .l4ca li.custom-cart-item section {
		padding-left: 12px !important;
		flex: 1 !important;
	}

	.form-cart .l4ca li.custom-cart-item header h2,
	.form-cart .l4ca li.custom-cart-item header h2 a {
		font-size: 14px !important;
		line-height: 16px !important;
		margin-bottom: 4px !important;
	}

	.form-cart .l4ca li.custom-cart-item .price {
		font-size: 14px !important;
		margin: 2px 0 8px 0 !important;
	}

	.form-cart .l4ca li.custom-cart-item ul li {
		font-size: 12px !important;
		margin-bottom: 2px !important;
	}

	.form-cart .l4ca li.custom-cart-item footer {
		flex-direction: row !important;
		align-items: center !important;
		justify-content: space-between !important;
		padding-top: 8px !important;
	}

	.form-cart .l4ca li.custom-cart-item footer .input-amount input {
		width: 50px !important;
		padding: 4px 6px !important;
		font-size: 12px !important;
	}
}